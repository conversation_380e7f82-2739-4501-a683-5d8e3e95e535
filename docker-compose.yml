services:
  redis:
    image: redis:latest
    container_name: redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_REPLICATION_MODE=master
    networks:
      - redis_network
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redisinsight:
    image: redis/redisinsight:latest
    container_name: redisinsight
    restart: unless-stopped
    ports:
      - "${REDISINSIGHT_PORT:-5540}:5540"
    networks:
      - redis_network
    volumes:
      - redisinsight_data:/data
    depends_on:
      - redis

networks:
  redis_network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  redisinsight_data:
    driver: local