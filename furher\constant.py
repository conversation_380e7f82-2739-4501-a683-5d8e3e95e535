"""
Manage environment variables
"""

import os
from typing import Optional
from dotenv import load_dotenv

load_dotenv()

# Credentials
TOKEN: Optional[str] = os.environ.get("TOKEN")
VIRUSTOTAL_API_KEY: Optional[str] = os.environ.get("VIRUSTOTAL_API_KEY")

# Server management
WELCOME_CHANNEL_ID: Optional[str] = os.environ.get("WELCOME_CHANNEL_ID")
GOODBYE_CHANNEL_ID: Optional[str] = os.environ.get("GOODBYE_CHANNEL_ID")
DEFAULT_ROLE_ID: Optional[str] = os.environ.get("DEFAULT_ROLE_ID")

# Internationalization
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
I18N_PATH = os.path.join(BASE_DIR, "i18n", "vn.json")
