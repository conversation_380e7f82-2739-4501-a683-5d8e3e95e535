"""
Manage help command for displaying help information
"""

import os
from datetime import datetime, timezone
import nextcord
from nextcord.ext import commands


class HelpCogs(commands.Cog):
    """Help command for displaying help information"""

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.doc_dir = os.path.dirname(os.path.abspath(__file__))
        # Add some colors for different categories
        self.category_colors = {
            "help": 0x2F3136,  # Discord dark theme color
            "music": 0x1DB954,  # Spotify green
            "manager": 0x5865F2,  # Discord blue
            "gaming": 0xFEE75C,  # Yellow
            # Add more categories with their colors
        }
        self.default_color = 0x2F3136  # Default color for undefined categories

    def get_available_categories(self) -> list:
        """
        Get all available help categories by scanning markdown files in the documentation directory.

        Args:
            None

        Returns:
            list: A list of category names (without .md extension) found in the doc directory.
                  Returns empty list if an error occurs during directory scanning.

        Raises:
            None: Exceptions are caught and logged, returning empty list instead.
        """
        try:
            categories = [
                f.replace(".md", "")
                for f in os.listdir(self.doc_dir)
                if f.endswith(".md")
            ]
            return categories
        except Exception as e:
            print(f"Error getting categories: {e}")
            return []

    def read_md_file(self, filename: str = "help") -> str:
        """
        Read content of a markdown file from the documentation directory.

        Args:
            filename (str, optional): Name of the markdown file to read without extension. Defaults to "help".

        Returns:
            str: Content of the markdown file if successful, None if file doesn't exist or on error.
        """
        try:
            filepath = os.path.join(self.doc_dir, f"{filename}.md")

            if not os.path.exists(filepath):
                return None

            with open(filepath, "r", encoding="utf-8") as file:
                return file.read()
        except Exception as e:
            print(f"Error reading file: {e}")
            return None

    def parse_md_content(self, content: str) -> tuple:
        """
        Parse markdown content into a title and sections.

        Args:
            content (str): The markdown content to parse.

        Returns:
            tuple: A tuple containing:
                - title (str or None): The main title from the markdown (text after first #)
                - sections (list or None): List of dictionaries containing parsed sections, where each dictionary has:
                    - name (str): Section name (text after ##)
                    - content (list): List of strings containing the section content lines
                Returns (None, None) if content is empty

        Example markdown format:
            # Main Title
            ## Section 1
            Content for section 1
            ## Section 2
            Content for section 2
        """
        if not content:
            return None, None

        lines = content.split("\n")
        title = None
        sections = []
        current_section = {"name": None, "content": []}

        for line in lines:
            if line.startswith("# "):
                title = line[2:].strip()
            elif line.startswith("## "):
                if current_section["name"]:
                    sections.append(current_section)
                current_section = {"name": line[3:].strip(), "content": []}
            elif current_section["name"]:
                current_section["content"].append(line)

        if current_section["name"]:
            sections.append(current_section)

        return title, sections

    def create_help_embed(self, category: str = None, content: str = None) -> list:
        """
        Create decorated embed(s) for help content, either showing general help or category-specific information.

        Args:
            category (str, optional): The help category to display. If None, shows general help. Defaults to None.
            content (str, optional): The markdown content for category-specific help. Defaults to None.

        Returns:
            list: List of nextcord.Embed objects containing formatted help content. Empty list if parsing fails.
        """
        embeds = []

        # If no category provided, show default help embed
        if not category:
            color = self.category_colors.get("help", self.default_color)
            main_embed = nextcord.Embed(
                title=self.bot.i18n.get("help_default_title"),
                color=color,
                timestamp=datetime.now(timezone.utc),
            )

            # Get all available categories for the list
            categories = self.get_available_categories()
            formatted_categories = []
            for cat in categories:
                if cat.lower() != "help":  # Skip help.md in the list
                    emoji = {
                        "music": "🎵",
                        "manager": "⚙️",
                        "gaming": "🎮",
                    }.get(cat.lower(), "📌")
                    formatted_categories.append(f"{emoji} **{cat.title()}**")

            # Set description with categories list
            main_embed.description = (
                f"{self.bot.i18n.get('help_default_description')}\n\n"
                + "\n".join(formatted_categories)
                + f"\n\n{self.bot.i18n.get('help_default_instruction')}"
            )

            main_embed.set_footer(
                text=self.bot.i18n.get("help_default_footer"),
                icon_url=self.bot.user.avatar.url if self.bot.user.avatar else None,
            )

            embeds.append(main_embed)
            return embeds

        # For specific categories, parse content and create embeds
        title, sections = self.parse_md_content(content)
        if not title:
            return []

        color = self.category_colors.get(category.lower(), self.default_color)

        # Main embed with title and description
        main_embed = nextcord.Embed(
            title=f"📚 {title}",
            color=color,
            timestamp=datetime.now(datetime.timezone.utc),
        )
        main_embed.set_footer(
            text=f"{self.bot.i18n.get('help_module_footer')}{category.title()}",
            icon_url=self.bot.user.avatar.url if self.bot.user.avatar else None,
        )

        # Add sections to embeds
        for i, section in enumerate(sections):
            if i == 0:
                # Add first section to main embed
                main_embed.description = "\n".join(section["content"])
                embeds.append(main_embed)
            else:
                # Create new embed for each additional section
                section_embed = nextcord.Embed(
                    title=f"{section['name']}",
                    description="\n".join(section["content"]),
                    color=color,
                )
                embeds.append(section_embed)

        return embeds

    @nextcord.slash_command(name="help", description="Display help information")
    async def help(
        self,
        interaction: nextcord.Interaction,
        category: str = nextcord.SlashOption(
            name="category",
            description="Category to get help for (e.g., music, manager)",
            required=False,
        ),
    ) -> None:
        """
        Generate and send help message embeds, either for a specific category or general help.

        Args:
            interaction (nextcord.Interaction): The interaction event from the slash command
            category (str, optional): Category name to get help for. Defaults to None.

        Returns:
            None: Sends help message embed(s) as response to the interaction
        """
        try:
            available_categories = self.get_available_categories()

            # Create and send embeds
            if category:
                # Check if category exists
                if category.lower() not in [
                    cat.lower() for cat in available_categories
                ]:
                    # Show default help embed instead of error for invalid category
                    embeds = self.create_help_embed()
                else:
                    content = self.read_md_file(category)
                    embeds = self.create_help_embed(category, content)
            else:
                # Show default help embed
                embeds = self.create_help_embed()

            if not embeds:
                error_embed = nextcord.Embed(
                    title="❌ Error",
                    description="Failed to create help message",
                    color=0xFF0000,
                )
                await interaction.response.send_message(
                    embed=error_embed, ephemeral=True
                )
                return

            # Send embeds
            await interaction.response.send_message(embeds=embeds[:10])

        except Exception as e:
            error_embed = nextcord.Embed(
                title="❌ Error",
                description=f"An error occurred: {str(e)}",
                color=0xFF0000,
            )
            await interaction.response.send_message(embed=error_embed, ephemeral=True)
