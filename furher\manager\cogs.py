"""
Command to manage the server
"""

from typing import Optional, ClassVar
from datetime import datetime
from dataclasses import dataclass
import nextcord
from nextcord.ext import commands
from nextcord.abc import GuildChannel
from furher.manager.utils import get_random_welcome_image, get_random_goodbye_image
from furher.constant import DEFAULT_ROLE_ID


@dataclass
class EmbedColors:
    """Constants for embed colors"""

    WELCOME: ClassVar[int] = 0x2ECC71  # Green
    GOODBYE: ClassVar[int] = 0xE74C3C  # Red


class ServerManageCogs(commands.Cog):
    """
    A cog that handles welcome and goodbye messages for server members.
    """

    def __init__(
        self, bot: commands.Bot, welcome_channel_id: int, goodbye_channel_id: int
    ) -> None:
        """
        Initialize the WelcomeGoodbye cog.

        Args:
            bot: The bot instance
            channel_id: The ID of the channel for welcome/goodbye messages
        """
        self.bot: commands.Bot = bot
        self.welcome_channel_id: int = welcome_channel_id
        self.goodbye_channel_id: int = goodbye_channel_id

    def get_welcome_channel(self) -> Optional[GuildChannel]:
        """
        Get the channel for sending welcome messages.

        Returns:
            Optional[GuildChannel]: The channel if found, None otherwise
        """
        return self.bot.get_channel(self.welcome_channel_id)

    def get_goodbye_channel(self) -> Optional[GuildChannel]:
        """
        Get the channel for sending goodbye messages.

        Returns:
            Optional[GuildChannel]: The channel if found, None otherwise
        """
        return self.bot.get_channel(self.goodbye_channel_id)

    async def create_welcome_embed(self, member: nextcord.Member) -> nextcord.Embed:
        """
        Create a welcome embed for a new member.

        Args:
            member: The member who joined

        Returns:
            nextcord.Embed: The formatted welcome embed
        """
        guild: nextcord.Guild = member.guild

        embed = nextcord.Embed(
            title=self.bot.i18n.get("manager_welcome_title"),
            description=f"{self.bot.i18n.get('manager_welcome_description_1')}{member.mention}{self.bot.i18n.get('manager_welcome_description_2')} **{guild.name}**!",
            color=EmbedColors.WELCOME,
            timestamp=datetime.now(),
        )

        embed.add_field(
            name=self.bot.i18n.get("manager_welcome_member_count"),
            value=f"{self.bot.i18n.get('manager_welcome_member_count_description')} #{len(guild.members)}!",
            inline=False,
        )

        embed.set_thumbnail(url=str(member.display_avatar.url))

        welcome_image = get_random_welcome_image()
        file = None
        if welcome_image:
            file = nextcord.File(welcome_image, filename="welcome.png")
            embed.set_image(url="attachment://welcome.png")

        if guild.icon:
            embed.set_footer(text=f"UID: {member.id}", icon_url=str(guild.icon.url))
        else:
            embed.set_footer(text=f"UID: {member.id}")

        return file, embed

    async def create_goodbye_embed(self, member: nextcord.Member) -> nextcord.Embed:
        """
        Create a goodbye embed for a leaving member.

        Args:
            member: The member who left

        Returns:
            nextcord.Embed: The formatted goodbye embed
        """
        guild: nextcord.Guild = member.guild

        embed = nextcord.Embed(
            title=self.bot.i18n.get("manager_goodbye_title"),
            description=f"**{member.name}** {self.bot.i18n.get('manager_goodbye_description')}",
            color=EmbedColors.GOODBYE,
            timestamp=datetime.now(),
        )

        embed.add_field(
            name=self.bot.i18n.get("manager_goodbye_member_count"),
            value=f"{self.bot.i18n.get('manager_welcome_member_count_description_1')} {len(guild.members)} {self.bot.i18n.get('manager_welcome_member_count_description_2')}",
            inline=False,
        )

        embed.set_thumbnail(url=str(member.display_avatar.url))

        goodbye_image = get_random_goodbye_image()
        file = None
        if goodbye_image:
            file = nextcord.File(goodbye_image, filename="goodbye.png")
            embed.set_image(url="attachment://goodbye.png")

        if guild.icon:
            embed.set_footer(text=f"UID: {member.id}", icon_url=str(guild.icon.url))
        else:
            embed.set_footer(text=f"UID: {member.id}")

        return file, embed

    @commands.Cog.listener()
    async def on_member_join(self, member: nextcord.Member) -> None:
        """
        Handles the event when a member joins the server by sending a welcome message and assigning default role.

        Args:
            member (nextcord.Member): The member object representing the user who joined the server

        Returns:
            None: This event handler sends a welcome message to the configured channel and assigns default role
        """
        channel = self.get_welcome_channel()
        if channel and isinstance(channel, nextcord.TextChannel):
            file, embed = await self.create_welcome_embed(member)
            await channel.send(file=file, embed=embed)
        try:
            default_role = member.guild.get_role(int(DEFAULT_ROLE_ID))
            if default_role:
                await member.add_roles(default_role)
        except nextcord.Forbidden:
            print(f"Bot doesn't have permission to assign roles in {member.guild.name}")
        except Exception as e:
            print(f"Error assigning role to {member.name}: {str(e)}")

    @commands.Cog.listener()
    async def on_member_remove(self, member: nextcord.Member) -> None:
        """
        Handles the event when a member leaves the server by sending a goodbye message.

        Args:
            member (nextcord.Member): The member object representing the user who left the server

        Returns:
            None: This event handler doesn't return anything, it sends a goodbye message to the configured channel
        """
        channel = self.get_goodbye_channel()
        if channel and isinstance(channel, nextcord.TextChannel):
            file, embed = await self.create_goodbye_embed(member)
            await channel.send(file=file, embed=embed)
