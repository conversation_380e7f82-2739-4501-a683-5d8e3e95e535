"""
Some util functions for the manager app
"""

from pathlib import Path
import random
import re
from typing import Union, Optional


def get_random_welcome_image(
    directory: Union[str, Path] = None,
) -> Optional[Path]:
    """
    Returns a random image file that follows the 'welcome[number]' naming pattern
    from the specified directory.

    Args:
        directory (Union[str, Path]): Path to the directory to search

    Returns:
        Optional[Path]: Path object of the randomly selected image file,
                       or None if no matching files are found
    """

    # resolve default directory
    if directory is None:
        current_file = Path(__file__)
        directory = current_file.parent / "assets" / "welcome"

    extensions = {".png", ".jpg", ".jpeg", ".gif"}
    # Convert string path to Path object if necessary
    dir_path = Path(directory) if isinstance(directory, str) else directory

    # Validate directory
    if not dir_path.exists():
        print(f"Directory not found: {dir_path}")
        return None
    if not dir_path.is_dir():
        print(f"Path is not a directory: {dir_path}")
        return None

    # Compile regex pattern for matching welcome files
    # This will match 'welcome' followed by one or more digits
    pattern = re.compile(r"^welcome\d+$")

    # Get all matching welcome image files
    welcome_images = [
        file
        for file in dir_path.iterdir()
        if (
            file.is_file()
            and file.suffix.lower() in extensions
            and pattern.match(file.stem)
        )
    ]

    # Return random image if any found, otherwise None
    return random.choice(welcome_images) if welcome_images else None


def get_random_goodbye_image(
    directory: Union[str, Path] = None,
) -> Optional[Path]:
    """
    Returns a random image file that follows the 'bye[number]' naming pattern
    from the specified directory.

    Args:
        directory (Union[str, Path]): Path to the directory to search

    Returns:
        Optional[Path]: Path object of the randomly selected image file,
                       or None if no matching files are found
    """

    # resolve default directory
    if directory is None:
        current_file = Path(__file__)
        directory = current_file.parent / "assets" / "bye"

    extensions = {".png", ".jpg", ".jpeg", ".gif"}
    # Convert string path to Path object if necessary
    dir_path = Path(directory) if isinstance(directory, str) else directory

    # Validate directory
    if not dir_path.exists():
        print(f"Directory not found: {dir_path}")
        return None
    if not dir_path.is_dir():
        print(f"Path is not a directory: {dir_path}")
        return None

    # Compile regex pattern for matching welcome files
    # This will match 'bye' followed by one or more digits
    pattern = re.compile(r"^bye\d+$")

    # Get all matching bye image files
    bye_images = [
        file
        for file in dir_path.iterdir()
        if (
            file.is_file()
            and file.suffix.lower() in extensions
            and pattern.match(file.stem)
        )
    ]

    # Return random image if any found, otherwise None
    return random.choice(bye_images) if bye_images else None
