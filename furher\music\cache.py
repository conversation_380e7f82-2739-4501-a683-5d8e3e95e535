import os
import json
from typing import Optional, Dict, Any
from minio import Minio
from minio.error import S3Error
from minio.lifecycleconfig import LifecycleConfig, Rule, Expiration
from dotenv import load_dotenv


class MinIOCacheManager:
    """
    Simple MinIO cache manager for music file storage with automatic expiration.

    Files are automatically deleted based on the expiry policy set via environment variables.
    """

    def __init__(self):
        """Initialize the MinIO cache manager with environment configuration."""
        # Load environment variables
        load_dotenv()

        # MinIO configuration from environment variables
        endpoint = (
            os.getenv("MINIO_ENDPOINT", "http://localhost:9000")
            .replace("http://", "")
            .replace("https://", "")
        )
        self.access_key = os.getenv("MINIO_ROOT_USER", "minioadmin")
        self.secret_key = os.getenv("MINIO_ROOT_PASSWORD", "minioadmin")
        self.expiry_days = int(os.getenv("CACHE_EXPIRY_DAYS", 90))

        # Bucket configuration
        self.bucket_name = "music-cache"

        # Initialize MinIO client
        self._connect_minio(endpoint)

        # Setup cache bucket with expiration policy
        self._setup_cache_bucket()

    def _connect_minio(self, endpoint: str) -> None:
        """Establish connection to MinIO server."""
        try:
            self.client = Minio(
                endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=False,  # Set to True if using HTTPS
            )

            # Test the connection by listing buckets
            list(self.client.list_buckets())
            print(f"✅ Connected to MinIO at {endpoint}")

        except S3Error as e:
            print(f"❌ Failed to connect to MinIO: {e}")
            raise
        except Exception as e:
            print(f"❌ MinIO connection error: {e}")
            raise

    def _setup_cache_bucket(self) -> None:
        """Create cache bucket and set expiration policy."""
        try:
            # Create bucket if it doesn't exist
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                print(f"✅ Created cache bucket: {self.bucket_name}")
            else:
                print(f"✅ Cache bucket already exists: {self.bucket_name}")

            # Set expiration policy
            self._set_expiry_policy(self.expiry_days)

        except S3Error as e:
            print(f"❌ Error setting up cache bucket: {e}")
            raise
        except Exception as e:
            print(f"❌ Error setting up cache bucket: {e}")
            raise

    def _set_expiry_policy(self, days: int) -> None:
        """Set or update the expiration policy for the cache bucket."""
        try:
            from minio.lifecycleconfig import Filter

            # Create lifecycle rule for expiration
            rule = Rule(
                rule_id="CacheExpiry",
                rule_filter=Filter(prefix=""),  # Apply to all objects with empty prefix
                status="Enabled",
                expiration=Expiration(days=days),
            )

            # Create lifecycle configuration
            lifecycle_config = LifecycleConfig([rule])

            # Apply lifecycle configuration to bucket
            self.client.set_bucket_lifecycle(self.bucket_name, lifecycle_config)

            print(f"✅ Set cache expiration policy: {days} days")

        except S3Error as e:
            print(f"❌ Error setting expiration policy: {e}")
            raise
        except Exception as e:
            print(f"❌ Error setting expiration policy: {e}")
            raise

    def upload_file(self, file_path: str, object_name: str) -> Optional[str]:
        """
        Upload a file to the cache bucket.

        Args:
            file_path (str): Local path to the file to upload
            object_name (str): Name for the object in MinIO

        Returns:
            Optional[str]: URL of the uploaded file if successful, None otherwise
        """
        try:
            # Upload file to MinIO
            self.client.fput_object(self.bucket_name, object_name, file_path)

            # Generate URL for the uploaded file
            file_url = f"http://{self.client._base_url.netloc}/{self.bucket_name}/{object_name}"

            print(f"🗂️ Uploaded file to cache: {object_name}")
            return file_url

        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
            return None
        except S3Error as e:
            print(f"❌ Error uploading file: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error uploading file: {e}")
            return None

    def file_exists(self, object_name: str) -> bool:
        """
        Check if a file exists in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.client.stat_object(self.bucket_name, object_name)
            return True
        except S3Error as e:
            if e.code == "NoSuchKey":
                return False
            else:
                print(f"❌ Error checking file existence: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error checking file: {e}")
            return False

    def get_file_url(self, object_name: str) -> str:
        """
        Get the URL for a file in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            str: URL of the file
        """
        endpoint = f"{self.client._base_url.scheme}://{self.client._base_url.host}"
        if self.client._base_url.port:
            endpoint += f":{self.client._base_url.port}"
        return f"{endpoint}/{self.bucket_name}/{object_name}"

    def upload_metadata(self, object_name: str, metadata: Dict[str, Any]) -> bool:
        """
        Upload metadata JSON file for a cached audio file.

        Args:
            object_name (str): Base object name (e.g., "music/hash.mp3")
            metadata (dict): Metadata dictionary to store

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create metadata object name (replace .mp3 with .json)
            metadata_name = object_name.replace(".mp3", ".json")

            # Convert metadata to JSON bytes
            metadata_json = json.dumps(metadata).encode("utf-8")

            # Upload metadata as object
            from io import BytesIO

            self.client.put_object(
                self.bucket_name,
                metadata_name,
                BytesIO(metadata_json),
                len(metadata_json),
                content_type="application/json",
            )

            print(f"🗂️ Uploaded metadata: {metadata_name}")
            return True

        except Exception as e:
            print(f"❌ Error uploading metadata: {e}")
            return False

    def get_metadata(self, object_name: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a cached audio file.

        Args:
            object_name (str): Base object name (e.g., "music/hash.mp3")

        Returns:
            Optional[Dict[str, Any]]: Metadata dictionary if found, None otherwise
        """
        try:
            # Create metadata object name
            metadata_name = object_name.replace(".mp3", ".json")

            # Download metadata
            response = self.client.get_object(self.bucket_name, metadata_name)
            metadata_json = response.read().decode("utf-8")

            return json.loads(metadata_json)

        except S3Error as e:
            if e.code == "NoSuchKey":
                return None
            else:
                print(f"❌ Error getting metadata: {e}")
                return None
        except Exception as e:
            print(f"❌ Unexpected error getting metadata: {e}")
            return None
