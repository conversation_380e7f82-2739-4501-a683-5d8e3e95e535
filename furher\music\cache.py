import os
from typing import Optional
import boto3
from botocore.client import Config
from botocore.exceptions import ClientError, NoCredentialsError
from dotenv import load_dotenv


class MinIOCacheManager:
    """
    MinIO-based cache manager for handling music file storage with automatic expiration.

    This class manages a single cache bucket where music files are stored
    with configurable expiration policies.
    """

    def __init__(self):
        """Initialize the MinIO cache manager with environment configuration."""
        # Load environment variables
        load_dotenv()

        # MinIO configuration from environment variables
        self.endpoint_url = os.getenv("MINIO_ENDPOINT", "http://localhost:9000")
        self.access_key = os.getenv("MINIO_ROOT_USER", "minioadmin")
        self.secret_key = os.getenv("MINIO_ROOT_PASSWORD", "minioadmin")
        self.expiry_days = int(os.getenv("CACHE_EXPIRY_DAYS", 90))

        # Bucket configuration
        self.bucket_name = "music-cache"

        # Initialize MinIO client
        self._connect_minio()

        # Setup cache bucket with expiration policy
        self._setup_cache_bucket()

    def _connect_minio(self) -> None:
        """Establish connection to MinIO server."""
        try:
            self.client = boto3.client(
                "s3",
                endpoint_url=self.endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                config=Config(signature_version="s3v4"),
                region_name="us-east-1",
            )

            # Test the connection by listing buckets
            self.client.list_buckets()
            print(f"✅ Connected to MinIO at {self.endpoint_url}")

        except NoCredentialsError:
            print("❌ MinIO credentials not found or invalid")
            raise
        except ClientError as e:
            print(f"❌ Failed to connect to MinIO: {e}")
            raise
        except Exception as e:
            print(f"❌ MinIO connection error: {e}")
            raise

    def _setup_cache_bucket(self) -> None:
        """Create cache bucket and set expiration policy."""
        try:
            # Create bucket if it doesn't exist
            try:
                self.client.create_bucket(Bucket=self.bucket_name)
                print(f"✅ Created cache bucket: {self.bucket_name}")
            except ClientError as e:
                error_code = e.response["Error"]["Code"]
                if error_code == "BucketAlreadyOwnedByYou":
                    print(f"✅ Cache bucket already exists: {self.bucket_name}")
                else:
                    print(f"❌ Error creating bucket: {e}")
                    raise

            # Set expiration policy
            self._set_expiry_policy(self.expiry_days)

        except Exception as e:
            print(f"❌ Error setting up cache bucket: {e}")
            raise

    def _set_expiry_policy(self, days: int) -> None:
        """Set or update the expiration policy for the cache bucket."""
        try:
            lifecycle_policy = {
                "Rules": [
                    {
                        "ID": "CacheExpiry",
                        "Status": "Enabled",
                        "Filter": {"Prefix": ""},
                        "Expiration": {"Days": days},
                    }
                ]
            }

            self.client.put_bucket_lifecycle_configuration(
                Bucket=self.bucket_name, LifecycleConfiguration=lifecycle_policy
            )

            print(f"✅ Set cache expiration policy: {days} days")

        except ClientError as e:
            print(f"❌ Error setting expiration policy: {e}")
            raise

    def upload_file(self, file_path: str, object_name: str) -> Optional[str]:
        """
        Upload a file to the cache bucket.

        Args:
            file_path (str): Local path to the file to upload
            object_name (str): Name for the object in MinIO

        Returns:
            Optional[str]: URL of the uploaded file if successful, None otherwise
        """
        try:
            # Upload file to MinIO
            self.client.upload_file(file_path, self.bucket_name, object_name)

            # Generate URL for the uploaded file
            file_url = f"{self.endpoint_url}/{self.bucket_name}/{object_name}"

            print(f"🗂️ Uploaded file to cache: {object_name}")
            return file_url

        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
            return None
        except ClientError as e:
            print(f"❌ Error uploading file: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error uploading file: {e}")
            return None

    def download_file(self, object_name: str, file_path: str) -> bool:
        """
        Download a file from the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO
            file_path (str): Local path where to save the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.client.download_file(self.bucket_name, object_name, file_path)
            print(f"📥 Downloaded file from cache: {object_name}")
            return True

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchKey":
                print(f"❌ File not found in cache: {object_name}")
            else:
                print(f"❌ Error downloading file: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error downloading file: {e}")
            return False

    def file_exists(self, object_name: str) -> bool:
        """
        Check if a file exists in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.client.head_object(Bucket=self.bucket_name, Key=object_name)
            return True
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "404":
                return False
            else:
                print(f"❌ Error checking file existence: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error checking file: {e}")
            return False

    def delete_file(self, object_name: str) -> bool:
        """
        Delete a file from the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.client.delete_object(Bucket=self.bucket_name, Key=object_name)
            print(f"🗑️ Deleted file from cache: {object_name}")
            return True

        except ClientError as e:
            print(f"❌ Error deleting file: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error deleting file: {e}")
            return False

    def get_file_url(self, object_name: str) -> str:
        """
        Get the URL for a file in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            str: URL of the file
        """
        return f"{self.endpoint_url}/{self.bucket_name}/{object_name}"

    def update_expiry_days(self, days: int) -> bool:
        """
        Update the expiration policy for the cache bucket.

        Args:
            days (int): New expiration time in days

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._set_expiry_policy(days)
            self.expiry_days = days
            return True
        except Exception as e:
            print(f"❌ Error updating expiry policy: {e}")
            return False

    def get_current_expiry_days(self) -> Optional[int]:
        """
        Get the current expiration policy for the cache bucket.

        Returns:
            Optional[int]: Current expiry days if available, None otherwise
        """
        try:
            response = self.client.get_bucket_lifecycle_configuration(
                Bucket=self.bucket_name
            )
            rules = response.get("Rules", [])

            for rule in rules:
                if rule.get("ID") == "CacheExpiry" and rule.get("Status") == "Enabled":
                    expiration = rule.get("Expiration", {})
                    return expiration.get("Days")

            return None

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchLifecycleConfiguration":
                print("❌ No lifecycle configuration found")
            else:
                print(f"❌ Error getting expiry policy: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error getting expiry policy: {e}")
            return None

    def clear_cache(self) -> bool:
        """
        Remove all files from the cache bucket.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # List all objects in the bucket
            response = self.client.list_objects_v2(Bucket=self.bucket_name)

            if "Contents" not in response:
                print("🗑️ Cache is already empty")
                return True

            # Delete all objects
            objects_to_delete = [{"Key": obj["Key"]} for obj in response["Contents"]]

            self.client.delete_objects(
                Bucket=self.bucket_name, Delete={"Objects": objects_to_delete}
            )

            deleted_count = len(objects_to_delete)
            print(f"🗑️ Cleared {deleted_count} files from cache")
            return True

        except ClientError as e:
            print(f"❌ Error clearing cache: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error clearing cache: {e}")
            return False

    def get_cache_stats(self) -> dict:
        """
        Get statistics about the cache bucket.

        Returns:
            dict: Cache statistics including file count and total size
        """
        try:
            response = self.client.list_objects_v2(Bucket=self.bucket_name)

            if "Contents" not in response:
                return {"file_count": 0, "total_size_bytes": 0, "total_size_mb": 0.0}

            file_count = len(response["Contents"])
            total_size = sum(obj["Size"] for obj in response["Contents"])
            total_size_mb = total_size / (1024 * 1024)

            return {
                "file_count": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size_mb, 2),
            }

        except ClientError as e:
            print(f"❌ Error getting cache stats: {e}")
            return {"error": str(e)}
        except Exception as e:
            print(f"❌ Unexpected error getting cache stats: {e}")
            return {"error": str(e)}

    def close(self) -> None:
        """Close the MinIO connection."""
        # boto3 client doesn't require explicit closing
        print("🔌 MinIO connection closed")
