import os
from typing import Optional
from minio import Minio
from minio.error import S3Error
from minio.lifecycleconfig import LifecycleConfig, Rule, Expiration
from dotenv import load_dotenv


class MinIOCacheManager:
    """
    MinIO-based cache manager for handling music file storage with automatic expiration.

    This class manages a single cache bucket where music files are stored
    with configurable expiration policies.
    """

    def __init__(self):
        """Initialize the MinIO cache manager with environment configuration."""
        # Load environment variables
        load_dotenv()

        # MinIO configuration from environment variables
        endpoint = (
            os.getenv("MINIO_ENDPOINT", "http://localhost:9000")
            .replace("http://", "")
            .replace("https://", "")
        )
        self.access_key = os.getenv("MINIO_ROOT_USER", "minioadmin")
        self.secret_key = os.getenv("MINIO_ROOT_PASSWORD", "minioadmin")
        self.expiry_days = int(os.getenv("CACHE_EXPIRY_DAYS", 90))

        # Bucket configuration
        self.bucket_name = "music-cache"

        # Initialize MinIO client
        self._connect_minio(endpoint)

        # Setup cache bucket with expiration policy
        self._setup_cache_bucket()

    def _connect_minio(self, endpoint: str) -> None:
        """Establish connection to MinIO server."""
        try:
            self.client = Minio(
                endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=False,  # Set to True if using HTTPS
            )

            # Test the connection by listing buckets
            list(self.client.list_buckets())
            print(f"✅ Connected to MinIO at {endpoint}")

        except S3Error as e:
            print(f"❌ Failed to connect to MinIO: {e}")
            raise
        except Exception as e:
            print(f"❌ MinIO connection error: {e}")
            raise

    def _setup_cache_bucket(self) -> None:
        """Create cache bucket and set expiration policy."""
        try:
            # Create bucket if it doesn't exist
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                print(f"✅ Created cache bucket: {self.bucket_name}")
            else:
                print(f"✅ Cache bucket already exists: {self.bucket_name}")

            # Set expiration policy
            self._set_expiry_policy(self.expiry_days)

        except S3Error as e:
            print(f"❌ Error setting up cache bucket: {e}")
            raise
        except Exception as e:
            print(f"❌ Error setting up cache bucket: {e}")
            raise

    def _set_expiry_policy(self, days: int) -> None:
        """Set or update the expiration policy for the cache bucket."""
        try:
            # Create lifecycle rule for expiration
            rule = Rule(
                rule_id="CacheExpiry",
                rule_filter=None,  # Apply to all objects
                status="Enabled",
                expiration=Expiration(days=days),
            )

            # Create lifecycle configuration
            lifecycle_config = LifecycleConfig([rule])

            # Apply lifecycle configuration to bucket
            self.client.set_bucket_lifecycle(self.bucket_name, lifecycle_config)

            print(f"✅ Set cache expiration policy: {days} days")

        except S3Error as e:
            print(f"❌ Error setting expiration policy: {e}")
            raise
        except Exception as e:
            print(f"❌ Error setting expiration policy: {e}")
            raise

    def upload_file(self, file_path: str, object_name: str) -> Optional[str]:
        """
        Upload a file to the cache bucket.

        Args:
            file_path (str): Local path to the file to upload
            object_name (str): Name for the object in MinIO

        Returns:
            Optional[str]: URL of the uploaded file if successful, None otherwise
        """
        try:
            # Upload file to MinIO
            self.client.fput_object(self.bucket_name, object_name, file_path)

            # Generate URL for the uploaded file
            file_url = f"http://{self.client._base_url.netloc}/{self.bucket_name}/{object_name}"

            print(f"🗂️ Uploaded file to cache: {object_name}")
            return file_url

        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
            return None
        except S3Error as e:
            print(f"❌ Error uploading file: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error uploading file: {e}")
            return None

    def download_file(self, object_name: str, file_path: str) -> bool:
        """
        Download a file from the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO
            file_path (str): Local path where to save the file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.client.fget_object(self.bucket_name, object_name, file_path)
            print(f"📥 Downloaded file from cache: {object_name}")
            return True

        except S3Error as e:
            if e.code == "NoSuchKey":
                print(f"❌ File not found in cache: {object_name}")
            else:
                print(f"❌ Error downloading file: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error downloading file: {e}")
            return False

    def file_exists(self, object_name: str) -> bool:
        """
        Check if a file exists in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            bool: True if file exists, False otherwise
        """
        try:
            self.client.stat_object(self.bucket_name, object_name)
            return True
        except S3Error as e:
            if e.code == "NoSuchKey":
                return False
            else:
                print(f"❌ Error checking file existence: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error checking file: {e}")
            return False

    def delete_file(self, object_name: str) -> bool:
        """
        Delete a file from the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.client.remove_object(self.bucket_name, object_name)
            print(f"🗑️ Deleted file from cache: {object_name}")
            return True

        except S3Error as e:
            print(f"❌ Error deleting file: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error deleting file: {e}")
            return False

    def get_file_url(self, object_name: str) -> str:
        """
        Get the URL for a file in the cache bucket.

        Args:
            object_name (str): Name of the object in MinIO

        Returns:
            str: URL of the file
        """
        return f"http://{self.client._base_url.netloc}/{self.bucket_name}/{object_name}"

    def update_expiry_days(self, days: int) -> bool:
        """
        Update the expiration policy for the cache bucket.

        Args:
            days (int): New expiration time in days

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._set_expiry_policy(days)
            self.expiry_days = days
            return True
        except Exception as e:
            print(f"❌ Error updating expiry policy: {e}")
            return False

    def get_current_expiry_days(self) -> Optional[int]:
        """
        Get the current expiration policy for the cache bucket.

        Returns:
            Optional[int]: Current expiry days if available, None otherwise
        """
        try:
            lifecycle_config = self.client.get_bucket_lifecycle(self.bucket_name)

            for rule in lifecycle_config.rules:
                if rule.rule_id == "CacheExpiry" and rule.status == "Enabled":
                    if rule.expiration and rule.expiration.days:
                        return rule.expiration.days

            return None

        except S3Error as e:
            if e.code == "NoSuchLifecycleConfiguration":
                print("❌ No lifecycle configuration found")
            else:
                print(f"❌ Error getting expiry policy: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error getting expiry policy: {e}")
            return None

    def clear_cache(self) -> bool:
        """
        Remove all files from the cache bucket.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # List all objects in the bucket
            objects = list(self.client.list_objects(self.bucket_name))

            if not objects:
                print("🗑️ Cache is already empty")
                return True

            # Delete all objects
            object_names = [obj.object_name for obj in objects]

            for obj_name in object_names:
                self.client.remove_object(self.bucket_name, obj_name)

            deleted_count = len(object_names)
            print(f"🗑️ Cleared {deleted_count} files from cache")
            return True

        except S3Error as e:
            print(f"❌ Error clearing cache: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error clearing cache: {e}")
            return False

    def get_cache_stats(self) -> dict:
        """
        Get statistics about the cache bucket.

        Returns:
            dict: Cache statistics including file count and total size
        """
        try:
            objects = list(self.client.list_objects(self.bucket_name))

            if not objects:
                return {"file_count": 0, "total_size_bytes": 0, "total_size_mb": 0.0}

            file_count = len(objects)
            total_size = sum(obj.size for obj in objects)
            total_size_mb = total_size / (1024 * 1024)

            return {
                "file_count": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size_mb, 2),
            }

        except S3Error as e:
            print(f"❌ Error getting cache stats: {e}")
            return {"error": str(e)}
        except Exception as e:
            print(f"❌ Unexpected error getting cache stats: {e}")
            return {"error": str(e)}

    def close(self) -> None:
        """Close the MinIO connection."""
        # MinIO client doesn't require explicit closing
        print("🔌 MinIO connection closed")
