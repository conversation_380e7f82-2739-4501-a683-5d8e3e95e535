"""
Music bot commands
"""

import asyncio
from datetime import datetime
from typing import Op<PERSON>, Dict, <PERSON>ple
import nextcord
from nextcord.ext import commands
from furher.music.player import MusicPlayer


class MusicCog(commands.Cog):
    """
    Music commands
    """

    def __init__(self, bot: commands.Bot) -> None:
        self.bot: commands.Bot = bot
        self.players: Dict[int, MusicPlayer] = {}
        self.disconnect_tasks: Dict[int, asyncio.Task] = {}
        # Add task check interval in seconds
        self.countdown_time: int = 60  # Check every minute

    def get_player(self, guild_id: int) -> Optional[MusicPlayer]:
        """
        Return music player
        """
        return self.players.get(guild_id)

    async def disconnect_after_delay(
        self, guild_id: int, voice_client: nextcord.VoiceClient
    ) -> None:
        """
        Start countdown and disconnect if channel remains empty
        """
        try:
            # Log start of countdown
            print(
                f"Starting {self.countdown_time} second countdown for guild {guild_id}"
            )

            # Wait for the specified countdown time
            await asyncio.sleep(self.countdown_time)

            # Check again if we should still disconnect
            if await self.should_disconnect(voice_client):
                # Get player instance
                player = self.get_player(guild_id)
                if player and player.interaction:
                    # Create disconnect embed
                    embed = nextcord.Embed(
                        title=self.bot.i18n.get("music_auto_disconnect_title"),
                        description=self.bot.i18n.get("music_auto_disconnect_message"),
                        color=0x8B4513,  # Brown color
                        timestamp=datetime.now(),
                    )

                    # Send disconnect message
                    try:
                        await player.interaction.channel.send(embed=embed)
                    except Exception as e:
                        print(f"Failed to send disconnect message: {e}")
                if player:
                    # Stop current playback if any
                    if voice_client and voice_client.is_playing():
                        voice_client.stop()

                    # Cleanup player
                    await player.cleanup()

                    # Remove player from dictionary
                    if guild_id in self.players:
                        del self.players[guild_id]

                # Finally disconnect
                if voice_client and voice_client.is_connected():
                    await voice_client.disconnect(force=True)

        except asyncio.CancelledError:
            print(f"Disconnect task cancelled for guild {guild_id}")
            raise  # Re-raise to ensure proper task cleanup

        except Exception as e:
            print(f"Error in disconnect task for guild {guild_id}: {e}")
        finally:
            # Only clean up task reference if this is the current task
            current_task = self.disconnect_tasks.get(guild_id)
            if current_task and current_task == asyncio.current_task():
                del self.disconnect_tasks[guild_id]
                print(f"Cleaned up disconnect task reference for guild {guild_id}")

    def cancel_disconnect_task(self, guild_id: int) -> None:
        """
        Cancel any pending disconnect task for the guild
        """
        if guild_id in self.disconnect_tasks:
            task = self.disconnect_tasks[guild_id]
            if not task.done() and not task.cancelled():
                print(f"Cancelling disconnect task for guild {guild_id}")
                task.cancel()
            # Let the task clean up its own reference

    async def should_disconnect(self, voice_client: nextcord.VoiceClient) -> bool:
        """
        Check if the bot should disconnect from the voice channel
        """
        if not voice_client or not voice_client.channel:
            return True

        # Count members in the channel (excluding the bot)
        members = [m for m in voice_client.channel.members if not m.bot]
        return len(members) == 0

    @commands.Cog.listener()
    async def on_voice_state_update(
        self,
        member: nextcord.Member,
        before: nextcord.VoiceState,
        after: nextcord.VoiceState,
    ) -> None:
        """
        Handle voice state updates to check for empty channels
        """
        if member.bot:  # Ignore bot voice state changes
            return

        guild_id = member.guild.id
        voice_client = member.guild.voice_client

        if not voice_client:
            return

        # Handle the case where user leaves the bot's channel
        if before.channel == voice_client.channel and (
            after.channel is None or after.channel != voice_client.channel
        ):
            # Check if the channel is now empty
            if await self.should_disconnect(voice_client):
                print(f"Channel empty, starting disconnect timer for guild {guild_id}")
                # Cancel any existing timer first
                if guild_id in self.disconnect_tasks:
                    self.cancel_disconnect_task(guild_id)
                    # Wait a bit for the old task to clean up
                    await asyncio.sleep(0.1)
                # Start new disconnect timer
                self.disconnect_tasks[guild_id] = asyncio.create_task(
                    self.disconnect_after_delay(guild_id, voice_client)
                )

        # Handle the case where user joins the bot's channel
        elif after.channel == voice_client.channel:
            # Cancel disconnect timer if it exists
            print(
                f"User joined channel, cancelling disconnect timer for guild {guild_id}"
            )
            self.cancel_disconnect_task(guild_id)

    async def ensure_voice_connection(
        self, interaction: nextcord.Interaction
    ) -> Tuple[bool, str]:
        """
        Ensure bot is connected to voice channel and player is initialized
        """
        if not interaction.user.voice:
            return False, self.bot.i18n.get("music_vc_not_connect")

        channel = interaction.user.voice.channel
        guild_id = interaction.guild_id

        try:
            voice_client = interaction.guild.voice_client

            if not voice_client:
                # Connect to voice channel
                voice_client = await channel.connect()
            elif voice_client.channel != channel:
                # Move to the new channel and update voice client
                await voice_client.move_to(channel)

            # Cancel any pending disconnect task as we're actively connecting
            self.cancel_disconnect_task(guild_id)

            # Initialize or update player
            if guild_id not in self.players:
                self.players[guild_id] = MusicPlayer(bot=self.bot)

            # Always update the voice client reference
            self.players[guild_id].vc = voice_client
            # Update interaction reference
            self.players[guild_id].interaction = interaction

            return True, f"✅ Connected to {channel.name}"
        except Exception as e:
            # Cleanup if connection fails
            if guild_id in self.players:
                await self.cleanup_player(guild_id, voice_client)
            return False, self.bot.i18n.get("music_vc_cannot_connect") + f"{str(e)}"

    @nextcord.slash_command(
        name="play", description="Join voice channel and play a song from URL"
    )
    async def play(
        self,
        interaction: nextcord.Interaction,
        query: str = nextcord.SlashOption(
            name="query", description="URL", required=True
        ),
    ) -> None:
        """
        Join voice channel and play a song from URL
        """
        # Defer the response first
        await interaction.response.defer()

        # Ensure bot is in voice channel and player is initialized
        success, message = await self.ensure_voice_connection(interaction)
        if not success:
            await interaction.followup.send(message)
            return

        player = self.get_player(interaction.guild_id)
        if not player:
            await interaction.followup.send(self.bot.i18n.get("music_player_not_found"))
            return

        try:
            # Pass Discord context to the player
            result = await player.add_to_queue(
                url=query,
                user_id=str(interaction.user.id),
                guild_id=str(interaction.guild_id),
                channel_id=str(interaction.channel_id),
            )

            if result == "started_playing":
                # Song started playing immediately - the "Now Playing" card will be sent by the player
                await interaction.followup.send(f"🎵 Processing and playing: {query}")
            elif result == "added_to_queue":
                # Song added to queue
                queue_length = player.get_queue_length()
                await interaction.followup.send(
                    f"📋 Added to queue (position {queue_length + 1}): {query}"
                )
            else:
                # Error message
                await interaction.followup.send(f"❌ {result}")

        except Exception as e:
            await interaction.followup.send(
                self.bot.i18n.get("music_playing_error_unknown") + f"{str(e)}"
            )

    @nextcord.slash_command(name="skip", description="Skip the current song")
    async def skip(self, interaction: nextcord.Interaction) -> None:
        """
        Skip the current song
        """
        player = self.get_player(interaction.guild_id)

        if player and player.is_playing and player.vc:
            player.vc.stop()
            player.current = None  # Set current song to None

            await interaction.response.send_message(self.bot.i18n.get("music_skipped"))
        else:
            await interaction.response.send_message(
                self.bot.i18n.get("music_skip_error_not_playing")
            )

    @nextcord.slash_command(
        name="stop", description="Stop playing and clear all queues"
    )
    async def stop(self, interaction: nextcord.Interaction) -> None:
        """
        Clear all queues and stop playing
        """
        player = self.get_player(interaction.guild_id)

        if player and player.vc:
            player.current = None

            # Clear Redis tasks
            try:
                player.task_manager.clear_tasks()
                print("✅ Cleared Redis tasks via stop command")
            except Exception as e:
                print(f"❌ Error clearing Redis tasks: {e}")

            if player.vc.is_playing():
                player.vc.stop()
            player.is_playing = False
            await interaction.response.send_message(self.bot.i18n.get("music_stop"))
        else:
            await interaction.response.send_message(
                self.bot.i18n.get("music_stop_error")
            )

    @nextcord.slash_command(name="queue", description="Show the current queue")
    async def queue(self, interaction: nextcord.Interaction) -> None:
        """
        Check music queue
        """
        player = self.get_player(interaction.guild_id)

        # Check if there's anything playing or in queue
        queue_length = player.get_queue_length() if player else 0
        if not player or (not player.current and queue_length == 0):
            await interaction.response.send_message(
                self.bot.i18n.get("music_queue_empty")
            )
            return

        embed = nextcord.Embed(
            title=self.bot.i18n.get("music_queue_title"), color=nextcord.Color.blue()
        )

        # Show currently playing song
        if player.current:
            embed.add_field(
                name=self.bot.i18n.get("music_queue_playing"),
                value=f"{player.current.title}\n",
                inline=False,
            )
            if player.current.thumbnail:
                embed.set_thumbnail(url=player.current.thumbnail)
            embed.add_field(
                name=self.bot.i18n.get("music_queue_duration"),
                value=f"{player.current.duration // 60}:{player.current.duration % 60:02d}",
            )

        # Show queue information
        if queue_length > 0:
            # Show next song info if available
            next_song = player.peek_next_song()
            if next_song:
                next_title = next_song.get(
                    "song_title", next_song.get("song_url", "Unknown")
                )
                embed.add_field(
                    name=self.bot.i18n.get("music_queue_upnext"),
                    value=f"`1.` {next_title}\n... and {queue_length - 1} more songs"
                    if queue_length > 1
                    else f"`1.` {next_title}",
                    inline=False,
                )
            else:
                embed.add_field(
                    name=self.bot.i18n.get("music_queue_upnext"),
                    value=f"{queue_length} songs in queue",
                    inline=False,
                )
        else:
            embed.add_field(
                name=self.bot.i18n.get("music_queue_upnext"),
                value=self.bot.i18n.get("music_queue_upnext_empty"),
                inline=False,
            )

        await interaction.response.send_message(embed=embed)

    async def cleanup_player(
        self, guild_id: int, voice_client: nextcord.VoiceClient
    ) -> None:
        """
        Cleanup player resources before disconnecting
        """
        try:
            # Get the player instance
            player = self.get_player(guild_id)
            if player:
                # Stop current playback and clear queue
                if voice_client and voice_client.is_playing():
                    voice_client.stop()

                # Use the cleanup method from player
                await player.cleanup()

                # Remove player from dictionary
                del self.players[guild_id]

        except Exception as e:
            print(f"Error during player cleanup: {e}")
            # Still try to remove player from dictionary even if cleanup fails
            if guild_id in self.players:
                del self.players[guild_id]

    @nextcord.slash_command(
        name="leave", description="Leave the voice channel and cleanup resources"
    )
    async def leave(self, interaction: nextcord.Interaction) -> None:
        """
        Leave voice channel and cleanup all resources
        """
        await interaction.response.defer()

        voice_client = interaction.guild.voice_client
        if not voice_client:
            await interaction.followup.send(self.bot.i18n.get("music_leave_no_vc"))
            return

        try:
            channel_name = voice_client.channel.name

            # Cleanup player first
            await self.cleanup_player(interaction.guild_id, voice_client)

            # Then disconnect from voice channel
            if voice_client.is_connected():
                await voice_client.disconnect(force=True)

            await interaction.followup.send(
                self.bot.i18n.get("music_leave") + f"{channel_name}"
            )

        except Exception as e:
            await interaction.followup.send(
                self.bot.i18n.get("music_leave_error_unknown") + f"{str(e)}",
                ephemeral=True,
            )

    async def get_voice_latency(self, voice_client: nextcord.VoiceClient) -> float:
        """
        Get voice latency metrics
        Returns: voice_latency in seconds
        """
        return voice_client.latency

    @nextcord.slash_command(
        name="lag", description="Check voice chat latency and connection status"
    )
    async def lag(self, interaction: nextcord.Interaction) -> None:
        """
        Check voice chat latency and connection status
        """
        await interaction.response.defer()

        try:
            voice_client = interaction.guild.voice_client
            if not voice_client or not voice_client.is_connected():
                await interaction.followup.send(
                    self.bot.i18n.get("music_lag_vc_not_connect")
                )
                return

            # Get latency metrics
            bot_latency = round(self.bot.latency * 1000, 1)  # Convert to ms
            voice_latency = round(
                await self.get_voice_latency(voice_client) * 1000, 1
            )  # Convert to ms

            # Create embed with latency information
            embed = nextcord.Embed(
                title=self.bot.i18n.get("music_lag_connection_status"),
                color=self.get_latency_color(voice_latency),
            )

            # Add fields with formatted latency values
            embed.add_field(
                name=self.bot.i18n.get("music_lag_bot_latency"),
                value=f"```{bot_latency}ms```",
                inline=True,
            )
            embed.add_field(
                name=self.bot.i18n.get("music_lag_vc_latency"),
                value=f"```{voice_latency}ms```",
                inline=True,
            )

            # Add connection info
            embed.add_field(
                name=self.bot.i18n.get("music_lag_connection_info"),
                value=f"```\n{self.bot.i18n.get('music_lag_voice_channel')}: {voice_client.channel.name}\n"
                f"{self.bot.i18n.get('music_lag_connected')}: {voice_client.is_connected()}\n"
                f"{self.bot.i18n.get('music_lag_playing')}: {voice_client.is_playing()}\n"
                f"{self.bot.i18n.get('music_lag_paused')}: {voice_client.is_paused()}```",
                inline=False,
            )

            # Add connection quality indicator
            quality = self.get_connection_quality(voice_latency)
            embed.add_field(
                name=self.bot.i18n.get("music_lag_vc_connection_quality"),
                value=quality,
                inline=False,
            )

            quality_bot = self.get_connection_quality(bot_latency)
            embed.add_field(
                name=self.bot.i18n.get("music_lag_ws_connection_quality"),
                value=quality_bot,
                inline=False,
            )

            await interaction.followup.send(embed=embed)

        except Exception as e:
            await interaction.followup.send(
                self.bot.i18n.get("music_lag_error") + f"{str(e)}", ephemeral=True
            )

    def get_latency_color(self, latency_ms: float) -> nextcord.Color:
        """
        Get color based on latency value (in milliseconds)
        """
        if latency_ms < 50:
            return nextcord.Color.green()
        elif latency_ms < 100:
            return nextcord.Color.blue()
        elif latency_ms < 200:
            return nextcord.Color.gold()
        else:
            return nextcord.Color.red()

    def get_connection_quality(self, latency_ms: float) -> str:
        """
        Get connection quality indicator based on latency (in milliseconds)
        """
        if latency_ms < 50:
            return self.bot.i18n.get("music_lag_excellent") + " (< 50ms)"
        elif latency_ms < 100:
            return self.bot.i18n.get("music_lag_good") + " (50-100ms)"
        elif latency_ms < 200:
            return self.bot.i18n.get("music_lag_fair") + " (100-200ms)"
        else:
            return self.bot.i18n.get("music_lag_poor") + " (> 200ms)"

    def cog_unload(self) -> None:
        """
        Cleanup when cog is unloaded
        """
        # Cancel all pending disconnect tasks
        for guild_id in list(self.disconnect_tasks.keys()):
            self.cancel_disconnect_task(guild_id)

        # Cleanup all players
        for guild_id, player in list(self.players.items()):
            if player.vc:
                self.bot.loop.create_task(self.cleanup_player(guild_id, player.vc))
