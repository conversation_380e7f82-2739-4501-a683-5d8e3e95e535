"""
Music player class
"""

from typing import Optional, Dict, Any
import asyncio
import os
import hashlib
import nextcord
from nextcord.ext import commands
from furher.music.streamer import YTD<PERSON>Info, StreamManager
from furher.music.task import RedisMusicTaskManager
from furher.music.cache import MinIOCacheManager

current_dir = os.path.dirname(os.path.abspath(__file__))
ffmpeg_path = os.path.join(os.path.dirname(current_dir), "music", "bin", "ffmpeg.exe")


class MusicPlayer:
    """
    Music player class
    """

    def __init__(self, bot: commands.Bot) -> None:
        self.bot: commands.Bot = bot
        self.current: Optional[YTDLInfo] = None
        self.vc: Optional[nextcord.VoiceClient] = None
        self.interaction: Optional[nextcord.Interaction] = None
        self.is_playing: bool = False
        self.stream_manager = StreamManager()
        self.cleanup_event = asyncio.Event()
        self.cleanup_lock = asyncio.Lock()

        # Initialize Redis task manager - raise exception if it fails
        self.task_manager = RedisMusicTaskManager()
        print("✅ Redis task manager initialized")

        # Initialize MinIO cache manager
        self.cache_manager = MinIOCacheManager()
        print("✅ MinIO cache manager initialized")

    async def add_to_queue(
        self, url: str, user_id: str = "", guild_id: str = "", channel_id: str = ""
    ) -> str:
        """Add a song URL to the Redis queue"""
        # Check if cleanup is in progress
        if self.cleanup_event.is_set():
            return "Player is being cleaned up"

        # Check voice connection
        if not self.vc:
            return "Voice client not initialized"

        if not self.vc.is_connected():
            print("Attempting to reconnect...")
            reconnected = await self.reconnect()
            if not reconnected:
                return "Failed to connect to voice channel"

        # Always add only URL to Redis queue (consistent approach)
        success = self.task_manager.add_song_request(
            user_id=user_id,
            song_url=url,
            guild_id=guild_id,
            channel_id=channel_id,
        )

        if not success:
            raise Exception("Failed to add song to Redis queue")

        # If nothing is playing, start playing immediately
        if not self.is_playing and self.vc and self.vc.is_connected():
            await self.play_next()
            return "started_playing"
        else:
            return "added_to_queue"

    async def _send_now_playing_card(self) -> None:
        """Send 'Now Playing' card with song information"""
        if not self.current:
            return

        try:
            # Send to interaction channel if available
            if self.interaction and hasattr(self.interaction, "channel"):
                import nextcord

                embed = nextcord.Embed(
                    title="🎵 Now Playing",
                    description=f"[{self.current.title}]({self.current.url})",
                    color=nextcord.Color.green(),
                )
                if self.current.thumbnail:
                    embed.set_thumbnail(url=self.current.thumbnail)
                if self.current.duration > 0:
                    embed.add_field(
                        name="Duration",
                        value=f"{self.current.duration // 60}:{self.current.duration % 60:02d}",
                        inline=True,
                    )

                # Send to the channel
                await self.interaction.channel.send(embed=embed)
                print(f"📤 Sent 'Now Playing' card for: {self.current.title}")
            else:
                # Fallback to simple print
                print(f"🎵 Now playing: {self.current.title}")
        except Exception as e:
            print(f"❌ Error sending 'Now Playing' card: {e}")
            print(f"🎵 Now playing: {self.current.title}")

    async def play_next(self) -> None:
        """Play the next song from Redis queue"""
        if not self.vc:
            print("Voice client is not initialized")
            self.is_playing = False
            return

        if not self.vc.is_connected():
            print("Voice client is disconnected")
            try:
                # Attempt to reconnect if channel reference is still available
                if hasattr(self.vc, "channel"):
                    await self.vc.connect(reconnect=True, timeout=60)
                else:
                    self.is_playing = False
                    return
            except Exception as e:
                print(f"Failed to reconnect: {e}")
                self.is_playing = False
                return

        if self.cleanup_event.is_set():
            return

        # Get next song URL from Redis
        next_song_data = self.task_manager.get_song_request()

        if not next_song_data:
            self.is_playing = False
            return

        # Extract URL from Redis data
        song_url = next_song_data.get("song_url", "")
        if not song_url:
            print("❌ No song URL found in Redis data")
            await self.play_next()  # Try next song
            return

        print(f"🎵 Processing song: {song_url}")

        # Generate hash for URL
        url_hash = hashlib.md5(song_url.encode()).hexdigest()
        object_name = f"music/{url_hash}.mp3"

        # Check if file exists in MinIO cache
        if self.cache_manager.file_exists(object_name):
            print(f"✅ Found cached file: {object_name}")
            cached_url = self.cache_manager.get_file_url(object_name)

            # Get stream info for metadata
            self.current = await self.stream_manager.get_stream_info(song_url)
            if not self.current:
                print(f"❌ Failed to get stream info for: {song_url}")
                await self.play_next()
                return

            # Use cached file URL
            self.current.stream_url = cached_url
        else:
            print(f"❌ File not cached, downloading: {song_url}")

            # Download and cache the file
            self.current = await self.stream_manager.download_and_cache(
                song_url, self.cache_manager, object_name
            )
            if not self.current:
                print(f"❌ Failed to download and cache: {song_url}")
                await self.play_next()
                return

        print(f"✅ Ready to play: {self.current.title}")
        queue_length = self.task_manager.get_queue_length()
        print(f"Redis queue length: {queue_length}")
        self.is_playing = True

        try:
            ffmpeg_options = {
                "before_options": "-reconnect 1 -reconnect_streamed 1 -reconnect_delay_max 5",
                "options": (
                    "-vn -b:a 192k -bufsize 3072k -ar 48000 "
                    "-acodec pcm_s16le -f s16le -ac 2 -loglevel error"
                ),
            }

            audio_source = nextcord.FFmpegPCMAudio(
                source=self.current.stream_url, executable=ffmpeg_path, **ffmpeg_options
            )

            audio_source = nextcord.PCMVolumeTransformer(audio_source, volume=0.5)

            def after_playing(error: Optional[Exception]) -> None:
                if error:
                    print(f"Error in playback: {error}")
                if (
                    not self.cleanup_event.is_set()
                    and self.vc
                    and self.vc.is_connected()
                ):
                    asyncio.run_coroutine_threadsafe(
                        self._after_song_ends(),
                        self.vc.loop if self.vc else asyncio.get_event_loop(),
                    )

            self.vc.play(audio_source, after=after_playing)

            # Send "Now Playing" card with extracted info
            await self._send_now_playing_card()
        except nextcord.ConnectionClosed:
            print("Voice connection was closed")
            self.is_playing = False
            # Attempt to reconnect
            if hasattr(self.vc, "channel"):
                try:
                    await self.vc.connect(reconnect=True, timeout=60)
                except Exception as e:
                    print(f"Failed to reconnect after connection closed: {e}")
        except Exception as e:
            print(f"Error playing audio: {e}")
            self.is_playing = False

    async def cleanup(self) -> None:
        """Clean up player resources with proper locking"""
        async with self.cleanup_lock:  # Ensure thread-safe cleanup
            if self.cleanup_event.is_set():  # Check if cleanup is already in progress
                return

            self.cleanup_event.set()  # Signal that cleanup is in progress

            try:
                # Stop current playback if any
                if self.vc:
                    if self.vc.is_playing():
                        self.vc.stop()
                    # Don't disconnect here - let the cog handle it
                    self.vc = None

                # Clear Redis tasks
                try:
                    self.task_manager.clear_tasks()
                    print("✅ Cleared Redis music tasks")
                except Exception as e:
                    print(f"❌ Error clearing Redis tasks: {e}")

                # Reset state
                self.current = None
                self.is_playing = False
                self.interaction = None

                # Close Redis connection
                try:
                    self.task_manager.close()
                    print("🔌 Closed Redis connection")
                except Exception as e:
                    print(f"❌ Error closing Redis connection: {e}")

            except Exception as e:
                print(f"Error during player cleanup: {e}")
            finally:
                # Reset cleanup event (in case player is reused)
                self.cleanup_event.clear()

    async def _after_song_ends(self) -> None:
        """Handle logic after a song ends"""
        # Check if cleanup is in progress
        if self.cleanup_event.is_set():
            return

        self.is_playing = False
        self.current = None
        await self.play_next()

    async def reconnect(self) -> bool:
        """Attempt to reconnect to voice channel"""
        if not self.vc or not hasattr(self.vc, "channel"):
            return False

        try:
            if not self.vc.is_connected():
                await self.vc.connect(reconnect=True, timeout=60)
            return True
        except Exception as e:
            print(f"Failed to reconnect: {e}")
            return False

    def get_queue_length(self) -> int:
        """Get the current queue length from Redis"""
        return self.task_manager.get_queue_length()

    def peek_next_song(self) -> Optional[Dict[str, Any]]:
        """Peek at the next song without removing it"""
        return self.task_manager.peek_next_request()
