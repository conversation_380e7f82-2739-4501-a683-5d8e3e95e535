"""
Audio stream manager
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import asyncio
import yt_dlp


@dataclass
class YTDLInfo:
    """
    Audio info class
    """

    url: str
    title: str
    stream_url: str
    thumbnail: str
    duration: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "YTDLInfo":
        return cls(
            url=data["webpage_url"] if "webpage_url" in data else data["url"],
            title=data["title"],
            stream_url=data["url"],
            thumbnail=data.get("thumbnail", ""),
            duration=data.get("duration", 0),
        )


class StreamManager:
    """
    Manage audio fetch
    """

    def __init__(self):
        self.ytdl = yt_dlp.YoutubeDL(
            {
                "format": "bestaudio/best",
                "extractaudio": True,
                "noplaylist": True,
                "nocheckcertificate": True,
                "ignoreerrors": False,
                "quiet": True,
                "no_warnings": True,
                "default_search": "auto",
                "source_address": "0.0.0.0",
            }
        )

    async def get_stream_info(self, url: str) -> Optional[YTDLInfo]:
        """Get stream information without downloading"""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(
                None, lambda: self.ytdl.extract_info(url, download=False)
            )
            return YTDLInfo.from_dict(info)
        except Exception as e:
            print(f"Error getting stream info for {url}: {e}")
            return None
