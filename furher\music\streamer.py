"""
Audio stream manager
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import asyncio
import tempfile
import os
import yt_dlp


@dataclass
class YTDLInfo:
    """
    Audio info class
    """

    url: str
    title: str
    stream_url: str
    thumbnail: str
    duration: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "YTDLInfo":
        return cls(
            url=data["webpage_url"] if "webpage_url" in data else data["url"],
            title=data["title"],
            stream_url=data["url"],
            thumbnail=data.get("thumbnail", ""),
            duration=data.get("duration", 0),
        )


class StreamManager:
    """
    Manage audio fetch
    """

    def __init__(self):
        self.ytdl = yt_dlp.YoutubeDL(
            {
                "format": "bestaudio/best",
                "extractaudio": True,
                "noplaylist": True,
                "nocheckcertificate": True,
                "ignoreerrors": False,
                "quiet": True,
                "no_warnings": True,
                "default_search": "auto",
                "source_address": "0.0.0.0",
            }
        )

    async def get_stream_info(self, url: str) -> Optional[YTDLInfo]:
        """Get stream information without downloading"""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(
                None, lambda: self.ytdl.extract_info(url, download=False)
            )
            return YTDLInfo.from_dict(info)
        except Exception as e:
            print(f"Error getting stream info for {url}: {e}")
            return None

    async def download_and_cache(
        self, url: str, cache_manager, object_name: str
    ) -> Optional[YTDLInfo]:
        """Download audio file and cache it in MinIO"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                temp_path = temp_file.name

            # Download with yt-dlp
            download_opts = {
                "format": "bestaudio/best",
                "extractaudio": True,
                "audioformat": "mp3",
                "outtmpl": temp_path,
                "noplaylist": True,
                "nocheckcertificate": True,
                "ignoreerrors": False,
                "quiet": True,
                "no_warnings": True,
            }

            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(
                None,
                lambda: yt_dlp.YoutubeDL(download_opts).extract_info(
                    url, download=True
                ),
            )

            # Upload audio file to MinIO
            cached_url = cache_manager.upload_file(temp_path, object_name)
            if not cached_url:
                return None

            # Create YTDLInfo with cached URL
            ytdl_info = YTDLInfo.from_dict(info)
            ytdl_info.stream_url = cached_url

            # Upload metadata to MinIO
            metadata = {
                "url": ytdl_info.url,
                "title": ytdl_info.title,
                "thumbnail": ytdl_info.thumbnail,
                "duration": ytdl_info.duration,
            }
            cache_manager.upload_metadata(object_name, metadata)

            # Clean up temp file
            os.unlink(temp_path)

            return ytdl_info

        except Exception as e:
            print(f"Error downloading and caching {url}: {e}")
            # Clean up temp file if it exists
            try:
                os.unlink(temp_path)
            except Exception as _:
                pass
            return None
