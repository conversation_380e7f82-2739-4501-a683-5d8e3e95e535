import json
import os
from typing import Dict, Optional, Any
import redis
from dotenv import load_dotenv


class RedisMusicTaskManager:
    """
    Redis-based music task manager for handling song requests in a queue.

    This class manages a Redis-based task queue where song requests are stored
    and processed in FIFO (First In, First Out) order.
    """

    def __init__(self):
        """Initialize the Redis music task manager with environment configuration."""
        # Load environment variables
        load_dotenv()

        # Redis configuration from environment variables
        self.redis_host = os.getenv("REDIS_HOST", "localhost")
        self.redis_port = int(os.getenv("REDIS_PORT", 6379))
        self.redis_password = os.getenv("REDIS_PASSWORD", None)
        self.redis_db = int(os.getenv("REDIS_DB", 0))

        # Redis key for the music task queue
        self.queue_key = "music:song_requests"

        # Initialize Redis connection
        self._connect_redis()

    def _connect_redis(self) -> None:
        """Establish connection to Redis server."""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                password=self.redis_password if self.redis_password else None,
                db=self.redis_db,
                decode_responses=True,
            )
            # Test the connection
            self.redis_client.ping()
            print(f"✅ Connected to Redis at {self.redis_host}:{self.redis_port}")
        except redis.ConnectionError as e:
            print(f"❌ Failed to connect to Redis: {e}")
            raise
        except Exception as e:
            print(f"❌ Redis connection error: {e}")
            raise

    def add_song_request(
        self,
        user_id: str,
        song_url: str,
        guild_id: str = "",
        channel_id: str = "",
    ) -> bool:
        """
        Add a new song request to the Redis queue.

        Args:
            user_id (str): Discord user ID who requested the song
            song_url (str): URL of the song to be played
            guild_id (str, optional): Discord guild (server) ID
            channel_id (str, optional): Discord channel ID

        Returns:
            bool: True if successfully added, False otherwise
        """
        try:
            # Create simplified task data - only store URL and metadata
            task_data = {
                "user_id": user_id,
                "song_url": song_url,
                "guild_id": guild_id,
                "channel_id": channel_id,
                "timestamp": self.redis_client.time()[0],  # Unix timestamp
            }

            # Convert to JSON and push to Redis list (queue)
            task_json = json.dumps(task_data)
            result = self.redis_client.lpush(self.queue_key, task_json)

            print(f"🎵 Added song request: {song_url} by user {user_id}")
            return result > 0

        except Exception as e:
            print(f"❌ Error adding song request: {e}")
            return False

    def get_song_request(self) -> Optional[Dict[str, Any]]:
        """
        Get and remove the next song request from the Redis queue.

        Returns:
            Optional[Dict[str, Any]]: Song request data if available, None if queue is empty
        """
        try:
            # Pop from the right side of the list (FIFO behavior)
            task_json = self.redis_client.rpop(self.queue_key)

            if task_json is None:
                return None

            # Parse JSON data
            task_data = json.loads(task_json)
            print(f"🎵 Retrieved song request: {task_data.get('song_url')}")
            return task_data

        except json.JSONDecodeError as e:
            print(f"❌ Error parsing song request JSON: {e}")
            return None
        except Exception as e:
            print(f"❌ Error retrieving song request: {e}")
            return None

    def clear_tasks(self) -> bool:
        """
        Remove all song requests from the Redis queue.

        Returns:
            bool: True if successfully cleared, False otherwise
        """
        try:
            # Delete the entire queue
            deleted_count = self.redis_client.delete(self.queue_key)
            print(f"🗑️ Cleared {deleted_count} song requests from queue")
            return True

        except Exception as e:
            print(f"❌ Error clearing song requests: {e}")
            return False

    def get_queue_length(self) -> int:
        """
        Get the current number of song requests in the queue.

        Returns:
            int: Number of pending song requests
        """
        try:
            return self.redis_client.llen(self.queue_key)
        except Exception as e:
            print(f"❌ Error getting queue length: {e}")
            return 0

    def peek_next_request(self) -> Optional[Dict[str, Any]]:
        """
        Peek at the next song request without removing it from the queue.

        Returns:
            Optional[Dict[str, Any]]: Next song request data if available, None if queue is empty
        """
        try:
            # Get the rightmost element without removing it
            task_json = self.redis_client.lindex(self.queue_key, -1)

            if task_json is None:
                return None

            return json.loads(task_json)

        except json.JSONDecodeError as e:
            print(f"❌ Error parsing song request JSON: {e}")
            return None
        except Exception as e:
            print(f"❌ Error peeking at next request: {e}")
            return None

    def close(self) -> None:
        """Close the Redis connection."""
        if hasattr(self, "redis_client"):
            self.redis_client.close()
            print("🔌 Redis connection closed")
