"""
Prepare bot to run
"""

import nextcord
from nextcord.ext import commands
from furher.music.cogs import MusicCog
from furher.manager.cogs import ServerManageCogs
from furher.constant import WELCOME_CHANNEL_ID, GOODBYE_CHANNEL_ID
from furher.utils import load_translations
from furher.doc.cogs import HelpCogs


def setup_bot() -> commands.Bot:
    """
    Setup bot with all required intents and cogs
    Returns:
        commands.Bot: Configured bot instance
    """
    # Set up intents
    intents = nextcord.Intents.default()
    intents.message_content = True
    intents.members = True  # Required for member join/leave events
    intents.guilds = True  # Required for guild-related events

    bot: commands.Bot = commands.Bot(intents=intents)
    print("Loading translations...")
    bot.i18n = load_translations()

    @bot.event
    async def on_ready() -> None:
        """Handler for when bot is ready"""
        if not bot.user:
            print("Bot user is None!")
            return

        print(f"Logged in as {bot.user.name}")
        await bot.change_presence(
            activity=nextcord.Activity(
                type=nextcord.ActivityType.listening,
                name=bot.i18n.get("status_default", "/help for commands"),
            )
        )

    # Validate welcome channel ID
    if not WELCOME_CHANNEL_ID:
        raise ValueError("WELCOME_CHANNEL_ID not found in environment variables")

    if not GOODBYE_CHANNEL_ID:
        raise ValueError("GOODBYE_CHANNEL_ID not found in environment variables")

    try:
        welcome_channel_id = int(WELCOME_CHANNEL_ID)
        goodbye_channel_id = int(GOODBYE_CHANNEL_ID)
    except ValueError as exc:
        raise ValueError("Invalid channel id") from exc

    print("Setting up help cog...")
    bot.add_cog(HelpCogs(bot))

    print("Setting up music cog...")
    bot.add_cog(MusicCog(bot))

    print("Setting up server management cog...")
    bot.add_cog(
        ServerManageCogs(
            bot=bot,
            welcome_channel_id=welcome_channel_id,
            goodbye_channel_id=goodbye_channel_id,
        )
    )

    return bot
